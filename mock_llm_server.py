#!/usr/bin/env python3
"""
模拟LLM服务器 - 用于测试和演示
当没有真实的本地模型服务时，可以使用此脚本模拟API响应
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uvicorn
import random
import time

app = FastAPI(title="Mock LLM Server", version="1.0.0")

class ChatMessage(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = -1
    stream: Optional[bool] = False

class ChatCompletionResponse(BaseModel):
    choices: List[Dict[str, Any]]
    model: str
    usage: Dict[str, int]

# 模拟响应模板
RESPONSE_TEMPLATES = {
    "discovery": [
        "感谢您选择我们的企业文化提炼服务！我是您的专业文化咨询师。\n\n为了更好地了解您的企业，我想先了解一下：您的企业主要从事什么业务？目标客户群体是谁？",
        "很好！您提到是{business}。接下来我想了解一下，您的创始团队有什么样的背景？他们最看重什么价值观念？",
        "非常有意思！基于您提到的团队背景，我想进一步了解：在日常工作中，您的员工最常提到企业的哪些特质？比如工作氛围、决策方式等。",
        "谢谢您的详细介绍！最后一个问题：您认为企业当前面临的最大挑战是什么？这将帮助我更好地理解企业的发展阶段。"
    ],
    "analysis": [
        "基于您提供的信息，我对您的企业进行了深入分析：\n\n**核心文化特征识别：**\n1. **技术驱动型文化** - 团队技术背景强，重视产品质量\n2. **客户导向** - 专注B端服务，注重客户价值\n3. **创新精神** - AI领域的探索体现了前瞻性思维\n4. **扁平化管理** - 小团队规模便于快速决策\n\n**潜在文化优势：**\n- 技术团队的专业性和创新能力\n- 对新技术的敏感度和学习能力\n- 灵活的组织结构和快速响应能力\n\n**需要关注的文化要素：**\n- 如何在快速发展中保持团队凝聚力\n- 技术文化与商业文化的平衡\n- 建立可持续的创新机制"
    ],
    "synthesis": [
        "基于深入分析，我为您提炼出以下企业文化框架：\n\n### 核心价值观\n- **技术匠心**：追求技术卓越，用匠心精神打磨每一个产品细节\n- **客户成功**：客户的成功就是我们的成功，持续创造客户价值\n- **敏捷创新**：拥抱变化，快速迭代，在不确定中寻找机遇\n- **协作共赢**：扁平沟通，开放协作，共同成长\n\n### 使命陈述\n用AI技术赋能企业，让智能成为生产力\n\n### 愿景展望\n成为最受信赖的AI工具提供商，引领智能办公新时代\n\n### 文化特色\n我们是一群技术理想主义者，用代码改变世界，用AI创造价值。我们相信技术的力量，更相信人与技术结合的无限可能。"
    ]
}

def get_mock_response(messages: List[ChatMessage]) -> str:
    """根据对话历史生成模拟响应"""
    
    # 分析对话轮数和内容，判断当前阶段
    user_messages = [msg for msg in messages if msg.role == "user"]
    conversation_length = len(user_messages)
    
    # 模拟思考时间
    time.sleep(random.uniform(0.5, 1.5))
    
    if conversation_length <= 2:
        # 初始阶段 - 信息收集
        template = random.choice(RESPONSE_TEMPLATES["discovery"][:2])
    elif conversation_length <= 4:
        # 继续收集信息
        template = random.choice(RESPONSE_TEMPLATES["discovery"][2:])
    elif conversation_length <= 6:
        # 分析阶段
        template = RESPONSE_TEMPLATES["analysis"][0]
    else:
        # 提炼阶段
        template = RESPONSE_TEMPLATES["synthesis"][0]
    
    # 简单的内容替换
    if "{business}" in template and user_messages:
        business_content = user_messages[0].content
        if "AI" in business_content or "科技" in business_content:
            template = template.replace("{business}", "AI科技公司")
        else:
            template = template.replace("{business}", "您的企业")
    
    return template

@app.post("/v1/chat/completions")
async def chat_completions(request: ChatCompletionRequest):
    """模拟聊天完成API"""
    try:
        # 生成模拟响应
        response_content = get_mock_response(request.messages)
        
        # 构建响应
        response = ChatCompletionResponse(
            choices=[
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": response_content
                    },
                    "finish_reason": "stop"
                }
            ],
            model=request.model,
            usage={
                "prompt_tokens": sum(len(msg.content) for msg in request.messages),
                "completion_tokens": len(response_content),
                "total_tokens": sum(len(msg.content) for msg in request.messages) + len(response_content)
            }
        )
        
        return response.dict()
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模拟服务错误: {str(e)}")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Mock LLM Server",
        "version": "1.0.0",
        "description": "模拟LLM服务器，用于测试企业文化提炼AI助手",
        "endpoints": {
            "chat": "/v1/chat/completions",
            "health": "/health"
        }
    }

@app.get("/health")
async def health():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "mock-llm-server",
        "models": ["qwen/qwen3-30b-a3b-2507"],
        "note": "这是一个模拟服务器，用于测试目的"
    }

@app.get("/v1/models")
async def list_models():
    """列出可用模型"""
    return {
        "data": [
            {
                "id": "qwen/qwen3-30b-a3b-2507",
                "object": "model",
                "created": **********,
                "owned_by": "mock-server"
            }
        ]
    }

if __name__ == "__main__":
    print("🤖 启动模拟LLM服务器...")
    print("📍 服务地址: http://localhost:2000")
    print("📚 API文档: http://localhost:2000/docs")
    print("⚠️  注意: 这是一个模拟服务器，仅用于测试目的")
    print()
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=2000,
        log_level="info"
    )
