# agents.py - 核心Agent系统实现
from typing import TypedDict, Dict, List, Any, Literal, AsyncGenerator
from langgraph.graph import StateGraph, END
import httpx
import json
import logging
from fastapi import HTTPException
from vector_store import get_vector_store

logger = logging.getLogger(__name__)

# ===== 1. 核心数据结构 =====
class CultureState(TypedDict):
    """所有Agent共享的会话状态"""
    user_id: str           # 用户标识
    session_id: str        # 会话ID
    messages: List[dict]   # 对话历史 [{"role": "user", "content": "..."}]
    current_phase: Literal["discovery", "knowledge_search", "analysis", "synthesis", "complete"]
    company_info: Dict[str, Any]  # 结构化企业信息
    culture_draft: Dict[str, Any] # 文化框架草稿
    knowledge_context: List[Dict[str, Any]]  # 从向量库获取的相关知识

# ===== 2. 本地模型调用封装 =====
async def call_local_llm(
    user_id: str,
    messages: List[dict],
    temperature: float = 0.7
) -> str:
    """统一调用本地模型（适配vLLM/Ollama API）"""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                "http://localhost:2000/v1/chat/completions",
                json={
                    "model": "qwen/qwen3-30b-a3b-2507",
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": -1,
                    "stream": False
                },
                timeout=30.0
            )
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            logger.error(f"模型调用失败 (user: {user_id}): {str(e)}")
            raise HTTPException(
                500, 
                f"模型调用失败 (user: {user_id}): {str(e)}"
            )

# ===== 3. 专业Agent实现 =====
async def discovery_agent(state: CultureState) -> CultureState:
    """信息收集Agent：引导用户描述企业基本情况"""
    system_prompt = """
    你是一个专业的企业文化咨询师，正在进行企业文化诊断的初始阶段。
    请通过3-5个问题收集以下关键信息：
    1. 企业核心业务和产品
    2. 创始团队背景和价值观
    3. 目标客户群体特征
    4. 企业当前面临的最大挑战
    5. 员工最常提到的企业特质

    要求：
    - 每次只问1个问题，循序渐进
    - 问题要具体、可回答
    - 避免专业术语，用日常语言
    - 如果用户回答模糊，要求澄清
    - 当收集到足够信息后，总结企业基本情况
    """

    # 构建上下文
    context = [
        {"role": "system", "content": system_prompt},
        *state["messages"][-6:]  # 仅传递最近对话
    ]

    # 调用本地模型
    response = await call_local_llm(
        user_id=state["user_id"],
        messages=context,
        temperature=0.3
    )

    # 判断是否收集到足够信息（简单规则：对话轮数超过8轮）
    should_proceed = len(state["messages"]) > 8
    
    # 如果收集到足够信息，提取企业基本信息
    company_info = state["company_info"].copy()
    if should_proceed:
        # 简单提取企业信息（实际应用中可以用更复杂的NLP技术）
        company_info["discovery_complete"] = True
        company_info["conversation_summary"] = json.dumps(state["messages"][-8:], ensure_ascii=False)

    # 返回更新后的状态
    return {
        **state,
        "messages": state["messages"] + [{"role": "assistant", "content": response}],
        "company_info": company_info,
        "current_phase": "knowledge_search" if should_proceed else "discovery"
    }

async def knowledge_search_agent(state: CultureState) -> CultureState:
    """知识搜索Agent：从向量库获取相关企业文化资料"""
    try:
        vector_store = get_vector_store()
        
        # 从对话历史中提取关键信息进行搜索
        recent_messages = state["messages"][-5:]
        search_query = " ".join([msg["content"] for msg in recent_messages if msg["role"] == "user"])
        
        # 搜索相关知识
        knowledge_items = vector_store.search_relevant_knowledge(
            query=search_query,
            n_results=5
        )
        
        # 构建知识上下文
        knowledge_context = []
        for item in knowledge_items:
            knowledge_context.append({
                "content": item["content"],
                "category": item["category"],
                "relevance": 1.0 - item["distance"]  # 转换为相关性分数
            })
        
        logger.info(f"为用户 {state['user_id']} 搜索到 {len(knowledge_context)} 条相关知识")
        
        return {
            **state,
            "knowledge_context": knowledge_context,
            "current_phase": "analysis"
        }
        
    except Exception as e:
        logger.error(f"知识搜索失败: {e}")
        # 如果知识搜索失败，直接进入分析阶段
        return {
            **state,
            "knowledge_context": [],
            "current_phase": "analysis"
        }

async def analysis_agent(state: CultureState) -> CultureState:
    """文化分析师Agent：结合知识库分析企业核心文化要素"""
    
    # 构建知识上下文
    knowledge_text = ""
    if state.get("knowledge_context"):
        knowledge_text = "\n\n参考知识：\n"
        for item in state["knowledge_context"]:
            knowledge_text += f"- {item['category']}: {item['content']}\n"
    
    system_prompt = f"""
    你是一个企业文化分析师，基于已收集的信息和专业知识识别核心文化模式。
    
    {knowledge_text}
    
    请分析：
    1. 企业表现出的核心价值观倾向
    2. 与行业标杆的异同点
    3. 潜在的文化冲突点
    4. 员工行为反映的隐性文化
    5. 结合参考知识，给出专业建议

    要求：
    - 基于事实，不主观臆断
    - 指出用户可能没意识到的模式
    - 用具体例子支持分析
    - 语言专业但易懂
    - 充分利用参考知识进行分析
    """

    # 构建上下文
    context = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"对话历史：{json.dumps(state['messages'][-10:], ensure_ascii=False)}"}
    ]

    # 调用本地模型
    response = await call_local_llm(
        user_id=state["user_id"],
        messages=context,
        temperature=0.4
    )

    # 更新公司信息
    company_info = state["company_info"].copy()
    company_info["analysis"] = response

    return {
        **state,
        "messages": state["messages"] + [{"role": "assistant", "content": response}],
        "company_info": company_info,
        "current_phase": "synthesis"
    }

async def synthesis_agent(state: CultureState) -> CultureState:
    """提炼专家Agent：生成文化框架"""
    
    # 构建知识上下文
    knowledge_text = ""
    if state.get("knowledge_context"):
        knowledge_text = "\n\n参考框架和最佳实践：\n"
        for item in state["knowledge_context"]:
            if item['category'] in ['价值观', '使命', '愿景', '分析方法']:
                knowledge_text += f"- {item['content']}\n"
    
    system_prompt = f"""
    你是一个企业文化提炼专家，基于分析结果和专业框架创建文化体系。
    
    {knowledge_text}
    
    请按以下结构输出：

    ### 核心价值观 (3-5条)
    - [价值观名称]：[15字内定义] - [50字内解释]

    ### 使命陈述
    [30字内，回答"我们为何存在"]

    ### 愿景展望
    [40字内，回答"我们要成为什么"]

    ### 文化特色
    [简要说明企业文化的独特之处]

    要求：
    - 价值观要独特、可行动、有区分度
    - 避免陈词滥调（如"创新、诚信"需具体化）
    - 语言简洁有力，体现企业特色
    - 基于事实，不凭空捏造
    - 参考专业框架，确保质量
    """

    # 构建上下文
    context = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"企业分析：{json.dumps(state['company_info'], ensure_ascii=False)}"}
    ]

    # 调用本地模型
    response = await call_local_llm(
        user_id=state["user_id"],
        messages=context,
        temperature=0.5
    )

    # 结构化结果
    culture_draft = {
        "raw_output": response,
        "phase": "synthesis",
        "knowledge_used": len(state.get("knowledge_context", []))
    }

    return {
        **state,
        "messages": state["messages"] + [{"role": "assistant", "content": response}],
        "culture_draft": culture_draft,
        "current_phase": "complete"
    }

# ===== 4. 工作流编排 =====
def build_culture_workflow():
    """构建多Agent工作流"""
    workflow = StateGraph(CultureState)

    # 注册所有Agent节点
    workflow.add_node("discovery", discovery_agent)
    workflow.add_node("knowledge_search", knowledge_search_agent)
    workflow.add_node("analysis", analysis_agent)
    workflow.add_node("synthesis", synthesis_agent)

    # 设置入口点
    workflow.set_entry_point("discovery")

    # 定义条件路由
    def route_next_step(state: CultureState):
        """根据当前状态决定下一步"""
        return state["current_phase"]

    # 添加条件边
    workflow.add_conditional_edges(
        "discovery",
        route_next_step,
        {
            "discovery": "discovery",
            "knowledge_search": "knowledge_search",
            "analysis": "analysis",
            "synthesis": "synthesis",
            "complete": END
        }
    )

    workflow.add_conditional_edges(
        "knowledge_search",
        route_next_step,
        {
            "knowledge_search": "knowledge_search",
            "analysis": "analysis",
            "synthesis": "synthesis",
            "complete": END
        }
    )

    workflow.add_conditional_edges(
        "analysis",
        route_next_step,
        {
            "analysis": "analysis",
            "synthesis": "synthesis",
            "complete": END
        }
    )

    workflow.add_conditional_edges(
        "synthesis",
        route_next_step,
        {
            "synthesis": "synthesis",
            "complete": END
        }
    )

    return workflow.compile()

# ===== 5. 用户上下文管理 =====
def get_user_config(user_id: str, session_id: str):
    """为每个用户会话创建独立配置"""
    return {
        "configurable": {
            "user_id": user_id,
            "session_id": session_id
        }
    }

# ===== 6. 流式输出生成器 =====
async def stream_culture_process(
    initial_state: CultureState
) -> AsyncGenerator[str, None]:
    """流式生成处理过程"""
    app = build_culture_workflow()

    try:
        # 逐步执行工作流
        async for output in app.astream(
            initial_state,
            config=get_user_config(
                initial_state["user_id"],
                initial_state["session_id"]
            ),
            stream_mode="values"
        ):
            # 仅当有新消息时流式输出
            if "messages" in output and len(output["messages"]) > len(initial_state["messages"]):
                new_message = output["messages"][-1]
                yield f"data: {json.dumps({'content': new_message['content'], 'phase': output['current_phase']}, ensure_ascii=False)}\n\n"

            # 更新初始状态用于比较
            initial_state = output

        # 完成信号
        yield "data: {\"event\": \"complete\"}\n\n"

        # 保存成功案例到知识库
        try:
            if initial_state.get("culture_draft") and initial_state["current_phase"] == "complete":
                vector_store = get_vector_store()
                vector_store.add_user_case(
                    initial_state["user_id"],
                    initial_state["company_info"],
                    initial_state["culture_draft"]
                )
        except Exception as e:
            logger.warning(f"保存用户案例失败: {e}")

    except Exception as e:
        logger.error(f"工作流执行失败: {e}")
        yield f"data: {{\"error\": \"{str(e)}\"}}\n\n"
