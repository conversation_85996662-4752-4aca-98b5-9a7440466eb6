#!/bin/bash

echo "启动企业文化提炼AI助手前端界面..."
echo

cd frontend

echo "检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请确保已安装Node.js 16+"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "错误: 未找到npm"
    exit 1
fi

node --version
npm --version

echo
echo "安装依赖包..."
npm install
if [ $? -ne 0 ]; then
    echo "错误: 依赖包安装失败"
    exit 1
fi

echo
echo "启动开发服务器..."
echo "前端将在 http://localhost:3000 启动"
echo

npm run dev
