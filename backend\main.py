# main.py - FastAPI后端服务
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from sse_starlette import EventSourceResponse
from agents import CultureState, stream_culture_process
from vector_store import get_vector_store
import uuid
import logging
from datetime import datetime
from typing import Dict, List, Optional
from pydantic import BaseModel

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="企业文化提炼AI助手",
    description="基于多Agent协同的企业文化提炼系统",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===== 内存会话存储 =====
# 格式: {user_id: {session_id: CultureState}}
SESSIONS: Dict[str, Dict[str, CultureState]] = {}

# ===== 请求模型 =====
class StartCultureRequest(BaseModel):
    user_id: str
    input: str = "你好，请帮我提炼企业文化"

class ContinueCultureRequest(BaseModel):
    user_id: str
    session_id: str
    input: str

class SessionInfo(BaseModel):
    session_id: str
    created_at: str
    current_phase: str
    last_message: str
    message_count: int

class UserSessionsResponse(BaseModel):
    user_id: str
    sessions: List[SessionInfo]

# ===== API端点 =====
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "企业文化提炼AI助手",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.post("/culture/start")
async def start_culture_process(request: StartCultureRequest):
    """启动新的文化提炼流程"""
    try:
        user_id = request.user_id
        if not user_id:
            raise HTTPException(400, "缺少user_id")

        user_input = request.input

        # 创建会话ID
        session_id = f"{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:6]}"

        # 创建初始状态
        initial_state: CultureState = {
            "user_id": user_id,
            "session_id": session_id,
            "messages": [{"role": "user", "content": user_input}],
            "current_phase": "discovery",
            "company_info": {},
            "culture_draft": {},
            "knowledge_context": []
        }

        # 保存会话
        if user_id not in SESSIONS:
            SESSIONS[user_id] = {}
        SESSIONS[user_id][session_id] = initial_state

        logger.info(f"启动新会话: user_id={user_id}, session_id={session_id}")

        # 返回流式响应
        return EventSourceResponse(
            stream_culture_process(initial_state),
            headers={"Cache-Control": "no-cache"}
        )

    except Exception as e:
        logger.error(f"启动文化提炼流程失败: {e}")
        raise HTTPException(500, f"启动流程失败: {str(e)}")

@app.post("/culture/continue")
async def continue_culture_process(request: ContinueCultureRequest):
    """继续已有文化提炼流程"""
    try:
        user_id = request.user_id
        session_id = request.session_id
        user_input = request.input

        if not user_id or not session_id:
            raise HTTPException(400, "缺少user_id或session_id")

        if not user_input:
            raise HTTPException(400, "缺少输入内容")

        # 检查会话是否存在
        if user_id not in SESSIONS or session_id not in SESSIONS[user_id]:
            raise HTTPException(404, "会话不存在")

        # 获取现有会话状态
        state = SESSIONS[user_id][session_id]

        # 添加用户消息
        updated_state = {
            **state,
            "messages": state["messages"] + [{"role": "user", "content": user_input}]
        }
        SESSIONS[user_id][session_id] = updated_state

        logger.info(f"继续会话: user_id={user_id}, session_id={session_id}")

        # 返回流式响应
        return EventSourceResponse(
            stream_culture_process(updated_state),
            headers={"Cache-Control": "no-cache"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"继续文化提炼流程失败: {e}")
        raise HTTPException(500, f"继续流程失败: {str(e)}")

@app.get("/sessions/{user_id}", response_model=UserSessionsResponse)
async def list_user_sessions(user_id: str):
    """列出用户的所有会话"""
    try:
        if user_id not in SESSIONS:
            return UserSessionsResponse(user_id=user_id, sessions=[])

        sessions = []
        for session_id, state in SESSIONS[user_id].items():
            # 获取最后一条消息
            last_message = ""
            if state["messages"]:
                last_msg = state["messages"][-1]["content"]
                last_message = last_msg[:50] + "..." if len(last_msg) > 50 else last_msg

            sessions.append(SessionInfo(
                session_id=session_id,
                created_at=session_id.split('-')[0],
                current_phase=state["current_phase"],
                last_message=last_message,
                message_count=len(state["messages"])
            ))

        # 按创建时间倒序排列
        sessions.sort(key=lambda x: x.session_id, reverse=True)

        return UserSessionsResponse(
            user_id=user_id,
            sessions=sessions
        )

    except Exception as e:
        logger.error(f"获取用户会话失败: {e}")
        raise HTTPException(500, f"获取会话失败: {str(e)}")

@app.get("/sessions/{user_id}/{session_id}")
async def get_session_detail(user_id: str, session_id: str):
    """获取会话详情"""
    try:
        if user_id not in SESSIONS or session_id not in SESSIONS[user_id]:
            raise HTTPException(404, "会话不存在")

        state = SESSIONS[user_id][session_id]
        
        return {
            "user_id": user_id,
            "session_id": session_id,
            "current_phase": state["current_phase"],
            "messages": state["messages"],
            "company_info": state["company_info"],
            "culture_draft": state["culture_draft"],
            "knowledge_context_count": len(state.get("knowledge_context", []))
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话详情失败: {e}")
        raise HTTPException(500, f"获取会话详情失败: {str(e)}")

@app.delete("/sessions/{user_id}/{session_id}")
async def delete_session(user_id: str, session_id: str):
    """删除会话"""
    try:
        if user_id not in SESSIONS or session_id not in SESSIONS[user_id]:
            raise HTTPException(404, "会话不存在")

        del SESSIONS[user_id][session_id]
        
        # 如果用户没有其他会话，删除用户记录
        if not SESSIONS[user_id]:
            del SESSIONS[user_id]

        logger.info(f"删除会话: user_id={user_id}, session_id={session_id}")
        
        return {"message": "会话已删除"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除会话失败: {e}")
        raise HTTPException(500, f"删除会话失败: {str(e)}")

# ===== 知识库管理端点 =====
@app.get("/knowledge/search")
async def search_knowledge(query: str, limit: int = 5):
    """搜索知识库"""
    try:
        vector_store = get_vector_store()
        results = vector_store.search_relevant_knowledge(query, n_results=limit)
        return {
            "query": query,
            "results": results,
            "count": len(results)
        }
    except Exception as e:
        logger.error(f"搜索知识库失败: {e}")
        raise HTTPException(500, f"搜索失败: {str(e)}")

@app.get("/knowledge/categories")
async def get_knowledge_categories():
    """获取知识库分类"""
    try:
        vector_store = get_vector_store()
        # 获取所有分类的知识
        categories = ["价值观", "使命", "愿景", "分析方法", "行业特色", "用户案例"]
        result = {}
        
        for category in categories:
            items = vector_store.get_category_knowledge(category)
            result[category] = len(items)
        
        return result
    except Exception as e:
        logger.error(f"获取知识库分类失败: {e}")
        raise HTTPException(500, f"获取分类失败: {str(e)}")

# ===== 调试端点 =====
@app.get("/debug/workflow")
async def debug_workflow():
    """调试端点：可视化工作流结构"""
    try:
        from agents import build_culture_workflow
        app_workflow = build_culture_workflow()

        # 生成Mermaid格式
        mermaid = """
        graph TD
            A[开始] --> B[信息收集Agent]
            B --> C{判断阶段}
            C -->|discovery| B
            C -->|knowledge_search| D[知识搜索Agent]
            D --> E[文化分析师Agent]
            E --> F{判断阶段}
            F -->|analysis| E
            F -->|synthesis| G[提炼专家Agent]
            G --> H{判断阶段}
            H -->|synthesis| G
            H -->|complete| I[完成]
        """

        return {
            "workflow_info": "多Agent企业文化提炼工作流",
            "phases": ["discovery", "knowledge_search", "analysis", "synthesis", "complete"],
            "agents": ["信息收集Agent", "知识搜索Agent", "文化分析师Agent", "提炼专家Agent"],
            "mermaid": mermaid.strip()
        }
    except Exception as e:
        logger.error(f"获取工作流信息失败: {e}")
        raise HTTPException(500, f"获取工作流信息失败: {str(e)}")

@app.get("/debug/stats")
async def debug_stats():
    """调试端点：系统统计信息"""
    try:
        total_users = len(SESSIONS)
        total_sessions = sum(len(sessions) for sessions in SESSIONS.values())
        
        # 统计各阶段会话数
        phase_stats = {}
        for user_sessions in SESSIONS.values():
            for state in user_sessions.values():
                phase = state["current_phase"]
                phase_stats[phase] = phase_stats.get(phase, 0) + 1
        
        # 知识库统计
        try:
            vector_store = get_vector_store()
            knowledge_count = vector_store.collection.count()
        except:
            knowledge_count = 0
        
        return {
            "total_users": total_users,
            "total_sessions": total_sessions,
            "phase_distribution": phase_stats,
            "knowledge_base_size": knowledge_count,
            "active_sessions": total_sessions
        }
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(500, f"获取统计信息失败: {str(e)}")

# ===== 健康检查 =====
@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查向量库
        vector_store = get_vector_store()
        knowledge_count = vector_store.collection.count()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "knowledge_base": "connected",
            "knowledge_count": knowledge_count
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
