#  企业文化提炼AI助手：MVP完整方案

## 一、功能设计

### 1. 核心功能

- **多用户支持**：每个用户独立会话，通过`user_id`区分
- **多Agent协同**：3个专业Agent分工合作完成文化提炼
- **多轮对话**：引导用户逐步提供企业信息并生成文化框架
- **流式输出**：实时显示AI思考过程（"打字机效果"）
- **会话管理**：支持查看历史会话和继续未完成流程

### 2. 用户工作流程

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant Agents

    User->>Frontend: 输入user_id和初始问题
    Frontend->>Backend: POST /culture/start
    Backend->>Agents: 启动工作流
    loop 多Agent协同
        Agents->>Agents: 信息收集Agent引导提问
        Agents->>Agents: 提炼专家Agent生成框架
        Agents->>Backend: 逐token返回结果
        Backend->>Frontend: SSE流式传输
        Frontend->>User: 实时显示AI回复
    end
    User->>Frontend: 继续对话
    Frontend->>Backend: 新消息
    Backend->>Agents: 继续工作流
```

### 3. Agent角色分工

| Agent          | 职责            | 交互特点         |
| -------------- | ------------- | ------------ |
| **信息收集Agent**  | 引导用户提供企业基础信息  | 每次问1个问题，循序渐进 |
| **文化分析师Agent** | 分析企业特点，识别文化要素 | 指出用户未意识到的模式  |
| **提炼专家Agent**  | 生成价值观、使命、愿景框架 | 语言精炼，避免陈词滥调  |

## 二、技术栈

### 前端

| 技术             | 用途   | 选择理由         |
| -------------- | ---- | ------------ |
| **React 18**   | UI框架 | 生态完善，适合复杂交互  |
| **TypeScript** | 类型系统 | 提高代码可维护性     |
| **Vite**       | 构建工具 | 极速开发体验       |
| **SSE Client** | 流式通信 | 原生浏览器支持，简单可靠 |

### 后端

| 技术            | 用途      | 选择理由       |
| ------------- | ------- | ---------- |
| **FastAPI**   | API框架   | 高性能，自动生成文档 |
| **LangGraph** | Agent编排 | 专为LLM工作流设计 |
| **httpx**     | 模型调用    | 异步HTTP客户端  |
| **SSE**       | 流式传输    | 浏览器原生支持    |

### AI层

| 技术            | 用途   | 选择理由       |
| ------------- | ---- | ---------- |
| **本地Qwen模型**  | 文本生成 | 中文优化，无需外网  |
| **vLLM兼容API** | 模型服务 | 高性能推理      |
| **TypedDict** | 状态管理 | 类型安全，IDE友好 |

## 三、关键代码实现

### 1. 核心Agent架构（`agents.py`）

```python
# agents.py - 单文件Agent核心实现
from typing import TypedDict, Dict, List, Any, Literal, AsyncGenerator
from langgraph.graph import StateGraph, END
import httpx
import json
from fastapi import HTTPException

# ===== 1. 核心数据结构 =====
class CultureState(TypedDict):
    """所有Agent共享的会话状态"""
    user_id: str           # 用户标识
    session_id: str        # 会话ID
    messages: List[dict]   # 对话历史 [{"role": "user", "content": "..."}]
    current_phase: Literal["discovery", "analysis", "synthesis", "complete"]
    company_info: Dict[str, Any]  # 结构化企业信息
    culture_draft: Dict[str, Any] # 文化框架草稿

# ===== 2. 本地模型调用封装 =====
async def call_local_llm(
    user_id: str,
    messages: List[dict],
    temperature: float = 0.7
) -> str:
    """统一调用本地模型（适配vLLM/Ollama API）"""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                "http://localhost:2000/v1/chat/completions",
                json={
                    "model": "qwen/qwen3-30b-a3b-2507",
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": -1,
                    "stream": False
                },
                timeout=30.0
            )
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            raise HTTPException(
                500, 
                f"模型调用失败 (user: {user_id}): {str(e)}"
            )

# ===== 3. 专业Agent实现 =====
async def discovery_agent(state: CultureState) -> CultureState:
    """信息收集Agent：引导用户描述企业基本情况"""
    system_prompt = """
    你是一个专业的企业文化咨询师，正在进行企业文化诊断的初始阶段。
    请通过3-5个问题收集以下关键信息：
    1. 企业核心业务和产品
    2. 创始团队背景和价值观
    3. 目标客户群体特征
    4. 企业当前面临的最大挑战
    5. 员工最常提到的企业特质

    要求：
    - 每次只问1个问题，循序渐进
    - 问题要具体、可回答
    - 避免专业术语，用日常语言
    - 如果用户回答模糊，要求澄清
    """

    # 构建上下文
    context = [
        {"role": "system", "content": system_prompt},
        *state["messages"][-6:]  # 仅传递最近对话
    ]

    # 调用本地模型
    response = await call_local_llm(
        user_id=state["user_id"],
        messages=context,
        temperature=0.3
    )

    # 返回更新后的状态
    return {
        **state,
        "messages": state["messages"] + [{"role": "assistant", "content": response}],
        # 当收集到足够信息后自动切换阶段
        "current_phase": "analysis" if len(state["messages"]) > 8 else "discovery"
    }

async def analysis_agent(state: CultureState) -> CultureState:
    """文化分析师Agent：识别企业核心文化要素"""
    system_prompt = """
    你是一个企业文化分析师，基于已收集的信息识别核心文化模式。
    请分析：
    1. 企业表现出的核心价值观倾向
    2. 与行业标杆的异同点
    3. 潜在的文化冲突点
    4. 员工行为反映的隐性文化

    要求：
    - 基于事实，不主观臆断
    - 指出用户可能没意识到的模式
    - 用具体例子支持分析
    - 语言专业但易懂
    """

    # 构建上下文
    context = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"对话历史：{json.dumps(state['messages'][-10:], ensure_ascii=False)}"}
    ]

    # 调用本地模型
    response = await call_local_llm(
        user_id=state["user_id"],
        messages=context,
        temperature=0.4
    )

    # 更新公司信息
    company_info = state["company_info"].copy()
    company_info["analysis"] = response

    return {
        **state,
        "messages": state["messages"] + [{"role": "assistant", "content": response}],
        "company_info": company_info,
        "current_phase": "synthesis"
    }

async def synthesis_agent(state: CultureState) -> CultureState:
    """提炼专家Agent：生成文化框架"""
    system_prompt = """
    你是一个企业文化提炼专家，基于分析结果创建专业文化框架。
    请按以下结构输出：

    ### 核心价值观 (3-5条)
    - [价值观名称]：[15字内定义] - [50字内解释]

    ### 使命陈述
    [30字内，回答"我们为何存在"]

    ### 愿景展望
    [40字内，回答"我们要成为什么"]

    要求：
    - 价值观要独特、可行动、有区分度
    - 避免陈词滥调（如"创新、诚信"需具体化）
    - 语言简洁有力，体现企业特色
    - 基于事实，不凭空捏造
    """

    # 构建上下文
    context = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"企业分析：{json.dumps(state['company_info'], ensure_ascii=False)}"}
    ]

    # 调用本地模型
    response = await call_local_llm(
        user_id=state["user_id"],
        messages=context,
        temperature=0.5
    )

    # 结构化结果
    return {
        **state,
        "messages": state["messages"] + [{"role": "assistant", "content": response}],
        "culture_draft": {
            "raw_output": response,
            "phase": "synthesis"
        },
        "current_phase": "complete"
    }

# ===== 4. 工作流编排 =====
def build_culture_workflow():
    """构建多Agent工作流"""
    workflow = StateGraph(CultureState)

    # 注册所有Agent节点
    workflow.add_node("discovery", discovery_agent)
    workflow.add_node("analysis", analysis_agent)
    workflow.add_node("synthesis", synthesis_agent)

    # 设置入口点
    workflow.set_entry_point("discovery")

    # 定义条件路由
    def route_next_step(state: CultureState):
        """根据当前状态决定下一步"""
        return state["current_phase"]

    # 添加条件边
    workflow.add_conditional_edges(
        "discovery",
        route_next_step,
        {
            "discovery": "discovery",
            "analysis": "analysis",
            "synthesis": "synthesis",
            "complete": END
        }
    )

    workflow.add_conditional_edges(
        "analysis",
        route_next_step,
        {
            "analysis": "analysis",
            "synthesis": "synthesis",
            "complete": END
        }
    )

    workflow.add_conditional_edges(
        "synthesis",
        route_next_step,
        {
            "synthesis": "synthesis",
            "complete": END
        }
    )

    return workflow.compile()

# ===== 5. 用户上下文管理 =====
def get_user_config(user_id: str, session_id: str):
    """为每个用户会话创建独立配置"""
    return {
        "configurable": {
            "user_id": user_id,
            "session_id": session_id
        }
    }

# ===== 6. 流式输出生成器 =====
async def stream_culture_process(
    initial_state: CultureState
) -> AsyncGenerator[str, None]:
    """流式生成处理过程"""
    app = build_culture_workflow()

    try:
        # 逐步执行工作流
        async for output in app.astream(
            initial_state,
            config=get_user_config(
                initial_state["user_id"], 
                initial_state["session_id"]
            ),
            stream_mode="values"
        ):
            # 仅当有新消息时流式输出
            if "messages" in output and len(output["messages"]) > len(initial_state["messages"]):
                new_message = output["messages"][-1]
                yield f"data: {json.dumps({'content': new_message['content'], 'phase': output['current_phase']})}\n\n"

            # 更新初始状态用于比较
            initial_state = output

        # 完成信号
        yield "data: {\"event\": \"complete\"}\n\n"

    except Exception as e:
        yield f"data: {{\"error\": \"{str(e)}\"}}\n\n"
```

### 2. FastAPI后端（`main.py`）

```python
# main.py
from fastapi import FastAPI, HTTPException, Request
from sse_starlette import EventSourceResponse
from agents import CultureState, stream_culture_process
import uuid
from datetime import datetime
from typing import Dict, List

app = FastAPI()

# ===== 内存会话存储 =====
# 格式: {user_id: {session_id: CultureState}}
SESSIONS: Dict[str, Dict[str, CultureState]] = {}

@app.post("/culture/start")
async def start_culture_process(request: Request):
    """启动新的文化提炼流程"""
    # 获取用户输入
    data = await request.json()
    user_id = data.get("user_id")
    if not user_id:
        raise HTTPException(400, "缺少user_id")

    user_input = data.get("input", "你好，请帮我提炼企业文化")

    # 创建会话ID
    session_id = f"{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:6]}"

    # 创建初始状态
    initial_state: CultureState = {
        "user_id": user_id,
        "session_id": session_id,
        "messages": [{"role": "user", "content": user_input}],
        "current_phase": "discovery",
        "company_info": {},
        "culture_draft": {}
    }

    # 保存会话
    if user_id not in SESSIONS:
        SESSIONS[user_id] = {}
    SESSIONS[user_id][session_id] = initial_state

    # 返回流式响应
    return EventSourceResponse(
        stream_culture_process(initial_state),
        headers={"Cache-Control": "no-cache"}
    )

@app.post("/culture/continue")
async def continue_culture_process(request: Request):
    """继续已有文化提炼流程"""
    # 获取用户输入
    data = await request.json()
    user_id = data.get("user_id")
    session_id = data.get("session_id")

    if not user_id or not session_id:
        raise HTTPException(400, "缺少user_id或session_id")

    user_input = data.get("input")
    if not user_input:
        raise HTTPException(400, "缺少输入内容")

    # 检查会话是否存在
    if user_id not in SESSIONS or session_id not in SESSIONS[user_id]:
        raise HTTPException(404, "会话不存在")

    # 获取现有会话状态
    state = SESSIONS[user_id][session_id]

    # 添加用户消息
    updated_state = {
        **state,
        "messages": state["messages"] + [{"role": "user", "content": user_input}]
    }
    SESSIONS[user_id][session_id] = updated_state

    # 返回流式响应
    return EventSourceResponse(
        stream_culture_process(updated_state),
        headers={"Cache-Control": "no-cache"}
    )

@app.get("/sessions/{user_id}")
async def list_user_sessions(user_id: str):
    """列出用户的所有会话"""
    if user_id not in SESSIONS:
        return {"user_id": user_id, "sessions": []}

    sessions = []
    for session_id, state in SESSIONS[user_id].items():
        sessions.append({
            "session_id": session_id,
            "created_at": session_id.split('-')[0],
            "current_phase": state["current_phase"],
            "last_message": state["messages"][-1]["content"][:50] + "..." 
                if state["messages"] else ""
        })

    return {
        "user_id": user_id,
        "sessions": sessions
    }

# ===== 调试端点 =====
@app.get("/debug/workflow")
async def debug_workflow():
    """调试端点：可视化工作流结构"""
    from agents import build_culture_workflow
    app = build_culture_workflow()

    # 生成Mermaid格式
    mermaid = """
    graph TD
        A[开始] --> B[信息收集Agent]
        B --> C{判断阶段}
        C -->|discovery| B
        C -->|analysis| D[文化分析师Agent]
        D --> C
        C -->|synthesis| E[提炼专家Agent]
        E --> C
        C -->|complete| F[完成]
    """

    return {
        "nodes": list(app.nodes.keys()),
        "edges": [
            {"from": edge.start_key, "to": edge.end_key, "condition": edge.condition}
            for edge in app.edges
        ],
        "mermaid": mermaid.strip()
    }
```

### 3. 前端实现（`frontend/src/App.tsx`）

```tsx
// frontend/src/App.tsx
import React, { useState, useEffect, useRef } from 'react';
import './App.css';

type ChatMessage = {
  role: 'user' | 'assistant';
  content: string;
};

type CulturePhase = 'discovery' | 'analysis' | 'synthesis' | 'complete';

type StreamEvent = {
  content?: string;
  phase?: CulturePhase;
  event?: string;
  error?: string;
};

function App() {
  const [userInput, setUserInput] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [phase, setPhase] = useState<CulturePhase>('discovery');
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [userSessions, setUserSessions] = useState<any[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  const userId = 'user_' + Math.random().toString(36).substr(2, 9);

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 获取用户会话列表
  useEffect(() => {
    fetchUserSessions();
  }, []);

  const fetchUserSessions = async () => {
    try {
      const response = await fetch(`http://localhost:8000/sessions/${userId}`);
      const data = await response.json();
      setUserSessions(data.sessions);
    } catch (error) {
      console.error('获取会话失败:', error);
    }
  };

  const startNewSession = () => {
    setMessages([]);
    setPhase('discovery');
    setSessionId(null);
    setUserInput('');
  };

  const loadSession = async (session: any) => {
    setSessionId(session.session_id);
    setPhase(session.current_phase);

    try {
      // 这里应该调用API获取完整会话，MVP简化处理
      const response = await fetch(`http://localhost:8000/sessions/${userId}`);
      const data = await response.json();
      const sessionData = data.sessions.find((s: any) => s.session_id === session.session_id);

      if (sessionData) {
        // 实际应用中应从后端获取完整消息历史
        setMessages([
          { role: 'user', content: '会话已加载' },
          { role: 'assistant', content: '这是之前的会话内容...' }
        ]);
      }
    } catch (error) {
      console.error('加载会话失败:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userInput.trim() || isLoading) return;

    // 添加用户消息
    const newMessage: ChatMessage = { role: 'user', content: userInput };
    setMessages(prev => [...prev, newMessage]);
    setUserInput('');

    setIsLoading(true);

    try {
      // 准备请求数据
      const requestData = {
        user_id: userId,
        input: userInput
      };

      if (sessionId) {
        // 继续已有会话
        const response = await fetch('http://localhost:8000/culture/continue', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...requestData, session_id: sessionId })
        });

        if (!response.ok) throw new Error('请求失败');
      } else {
        // 新会话
        const response = await fetch('http://localhost:8000/culture/start', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestData)
        });

        if (!response.ok) throw new Error('请求失败');

        // 解析会话ID（实际应用中后端应返回session_id）
        const reader = response.body?.getReader();
        if (reader) {
          const { value } = await reader.read();
          const text = new TextDecoder().decode(value);
          const match = text.match(/session_\w+/);
          if (match) setSessionId(match[0]);
        }
      }

      // 建立SSE连接
      const url = sessionId 
        ? `http://localhost:8000/culture/continue?user_id=${userId}&session_id=${sessionId}`
        : `http://localhost:8000/culture/start?user_id=${userId}`;

      eventSourceRef.current = new EventSource(url);

      eventSourceRef.current.onmessage = (event) => {
        try {
          const data: StreamEvent = JSON.parse(event.data);

          if (data.error) {
            setMessages(prev => [...prev, { 
              role: 'assistant', 
              content: `系统错误: ${data.error}` 
            }]);
            setIsLoading(false);
            eventSourceRef.current?.close();
            return;
          }

          if (data.event === 'complete') {
            setIsLoading(false);
            eventSourceRef.current?.close();
            fetchUserSessions();
            return;
          }

          if (data.content) {
            setMessages(prev => {
              const lastMsg = prev[prev.length - 1];
              if (lastMsg?.role === 'assistant') {
                return [
                  ...prev.slice(0, -1),
                  { role: 'assistant', content: lastMsg.content + data.content }
                ];
              }
              return [...prev, { role: 'assistant', content: data.content }];
            });
          }

          if (data.phase) {
            setPhase(data.phase);
          }
        } catch (error) {
          console.error('解析事件失败:', error);
        }
      };

      eventSourceRef.current.onerror = () => {
        setIsLoading(false);
        eventSourceRef.current?.close();
      };

    } catch (error) {
      console.error('请求失败:', error);
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: '抱歉，发生网络错误，请重试' 
      }]);
      setIsLoading(false);
    }
  };

  const renderPhaseBadge = () => {
    const phaseNames: Record<CulturePhase, string> = {
      discovery: '信息收集',
      analysis: '文化分析',
      synthesis: '框架提炼',
      complete: '完成'
    };

    const phaseColors: Record<CulturePhase, string> = {
      discovery: 'bg-blue-500',
      analysis: 'bg-purple-500',
      synthesis: 'bg-green-500',
      complete: 'bg-gray-500'
    };

    return (
      <span className={`px-2 py-1 rounded text-white text-xs ${phaseColors[phase]}`}>
        {phaseNames[phase]}
      </span>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-4">
        <header className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800">企业文化提炼助手</h1>
          <div className="mt-2 flex items-center justify-between">
            <div>
              <span className="text-sm text-gray-600">用户ID: {userId.substring(0, 10)}...</span>
            </div>
            <div className="flex items-center space-x-4">
              {renderPhaseBadge()}
              <button 
                onClick={startNewSession}
                className="px-3 py-1 bg-indigo-600 text-white rounded hover:bg-indigo-700 text-sm"
              >
                新建会话
              </button>
            </div>
          </div>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
          {/* 会话列表 */}
          <div className="lg:col-span-1 bg-white rounded-lg shadow overflow-hidden">
            <div className="p-3 border-b bg-gray-50">
              <h2 className="font-semibold">历史会话</h2>
            </div>
            <div className="overflow-y-auto" style={{ maxHeight: '60vh' }}>
              {userSessions.length === 0 ? (
                <div className="p-4 text-gray-500 text-sm">
                  暂无历史会话
                </div>
              ) : (
                userSessions.map((session) => (
                  <div 
                    key={session.session_id}
                    className={`p-3 border-b hover:bg-gray-50 cursor-pointer ${
                      sessionId === session.session_id ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => loadSession(session)}
                  >
                    <div className="font-medium truncate">{session.last_message}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      {session.created_at} • {session.current_phase}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 聊天主区域 */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow h-[70vh] flex flex-col">
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((msg, index) => (
                  <div 
                    key={index} 
                    className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`max-w-[80%] rounded-lg p-3 ${
                      msg.role === 'user' 
                        ? 'bg-indigo-600 text-white' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {msg.content}
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>

              <div className="border-t p-3">
                <form onSubmit={handleSubmit} className="flex gap-2">
                  <input
                    type="text"
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    placeholder="请输入您的企业信息..."
                    className="flex-1 p-2 border rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    disabled={isLoading}
                  />
                  <button
                    type="submit"
                    disabled={isLoading || !userInput.trim()}
                    className={`px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 ${
                      isLoading || !userInput.trim() ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    {isLoading ? '思考中...' : '发送'}
                  </button>
                </form>
                <p className="mt-2 text-xs text-gray-500">
                  正在使用Qwen3-30B模型为您服务
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
```

### 4. 项目结构

```
culture-ai/
├── backend/
│   ├── main.py            # FastAPI入口
│   ├── agents.py          # 核心Agent实现
│   └── requirements.txt   # 后端依赖
├── frontend/
│   ├── package.json
│   ├── vite.config.ts
│   └── src/
│       ├── App.tsx        # 主组件
│       ├── index.css
│       └── main.tsx
└── README.md
```

### 5. 后端依赖（`backend/requirements.txt`）

```
fastapi
uvicorn
langgraph
httpx
sse-starlette
python-multipart
```

### 6. 前端依赖（`frontend/package.json`）

```json
{
  "name": "culture-ai-frontend",
  "version": "0.0.1",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@vitejs/plugin-react": "^4.0.3",
    "typescript": "^5.0.2",
    "vite": "^4.4.5"
  }
}
```

## 四、使用说明

### 1. 环境准备

```bash
# 启动本地模型服务（示例）
# 使用vLLM或Ollama启动Qwen模型
python -m vllm.entrypoints.openai.api_server \
  --model qwen/qwen3-30b-a3b-2507 \
  --host 0.0.0.0 \
  --port 2000
```

### 2. 启动后端

```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --port 8000
```

### 3. 启动前端

```bash
cd frontend
npm install
npm run dev
```

### 4. 测试API

```bash
# 启动新会话
curl -N http://localhost:8000/culture/start \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "input": "我们是一家科技创业公司，主要做AI工具"
  }'

# 继续会话
curl -N http://localhost:8000/culture/continue \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "session_id": "20240615-abcdef",
    "input": "我们的团队有10人，都是技术背景"
  }'
```

## 五、MVP验证要点

1. **多用户隔离**：不同`user_id`的会话完全独立

2. **Agent协同**：3个Agent按流程顺序执行

3. **流式输出**：前端实时显示AI生成过程

4. **会话管理**：可查看和继续历史会话

5. **本地模型调用**：API调用本地Qwen模型 
   curl http://localhost:2000/v1/chat/completions \
   -H "Content-Type: application/json" \
   -d '{
    "model": "qwen/qwen3-30b-a3b-2507",
    "messages": [
      { "role": "system", "content": "Always answer in rhymes. Today is Thursday" },
      { "role": "user", "content": "What day is it today?" }
    ],
    "temperature": 0.7,
    "max_tokens": -1,
    "stream": false
   }'

## 六、架构优势

1. **单文件Agent核心**：所有Agent逻辑集中在`agents.py`，便于维护
2. **清晰的状态管理**：通过`CultureState`统一管理会话状态
3. **IDE友好调试**：每个Agent可独立测试，工作流可视化
4. **未来扩展性**：新增Agent只需添加节点和路由规则
5. **用户上下文隔离**：通过`user_id`和`session_id`实现多用户支持


