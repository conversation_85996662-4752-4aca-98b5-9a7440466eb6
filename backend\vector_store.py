# vector_store.py - 向量知识库管理
import os
import json
from typing import List, Dict, Any, Optional
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
import logging

logger = logging.getLogger(__name__)

class CultureVectorStore:
    """企业文化向量知识库"""
    
    def __init__(self, persist_directory: str = "./chroma_db"):
        """初始化向量数据库"""
        self.persist_directory = persist_directory
        
        # 初始化ChromaDB客户端
        self.client = chromadb.PersistentClient(
            path=persist_directory,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # 初始化嵌入模型
        try:
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        except Exception as e:
            logger.warning(f"无法加载嵌入模型: {e}，将使用默认嵌入")
            self.embedding_model = None
        
        # 获取或创建集合
        self.collection = self._get_or_create_collection()
        
        # 初始化知识库
        self._initialize_knowledge_base()
    
    def _get_or_create_collection(self):
        """获取或创建向量集合"""
        try:
            collection = self.client.get_collection("culture_knowledge")
        except:
            collection = self.client.create_collection(
                name="culture_knowledge",
                metadata={"description": "企业文化知识库"}
            )
        return collection
    
    def _initialize_knowledge_base(self):
        """初始化知识库内容"""
        # 检查是否已有数据
        if self.collection.count() > 0:
            logger.info(f"知识库已存在 {self.collection.count()} 条记录")
            return
        
        # 添加基础企业文化知识
        base_knowledge = [
            {
                "id": "values_definition",
                "content": "企业核心价值观是指导企业行为的基本信念和原则，应该具体、可操作、有区分度。避免使用'诚信、创新、团队合作'等通用词汇，而应该结合企业特色进行具体化表达。",
                "category": "价值观",
                "keywords": ["价值观", "核心理念", "行为准则"]
            },
            {
                "id": "mission_framework",
                "content": "企业使命回答'我们为何存在'的问题，应该简洁有力，体现企业的社会价值和存在意义。好的使命陈述通常在30字以内，能够激发员工的使命感。",
                "category": "使命",
                "keywords": ["使命", "存在意义", "社会价值"]
            },
            {
                "id": "vision_guidelines",
                "content": "企业愿景描述'我们要成为什么'，是企业未来的理想状态。应该具有挑战性但可实现，能够指引企业发展方向，通常在40字以内。",
                "category": "愿景",
                "keywords": ["愿景", "未来目标", "发展方向"]
            },
            {
                "id": "culture_analysis_method",
                "content": "企业文化分析需要从多个维度进行：1)显性文化（制度、流程、标语）2)隐性文化（员工行为、决策模式、沟通方式）3)深层文化（基本假设、价值观念）。要特别关注员工的真实感受和行为模式。",
                "category": "分析方法",
                "keywords": ["文化分析", "显性文化", "隐性文化", "深层文化"]
            },
            {
                "id": "startup_culture_traits",
                "content": "创业公司文化特点：快速迭代、扁平化管理、结果导向、创新精神、风险承担。需要平衡灵活性与规范性，既要保持创业活力，又要建立必要的制度框架。",
                "category": "行业特色",
                "keywords": ["创业公司", "快速迭代", "扁平化", "创新"]
            },
            {
                "id": "tech_company_culture",
                "content": "科技公司文化要素：技术驱动、数据决策、用户至上、持续学习、开放协作。重视技术能力和产品思维，鼓励实验和快速试错。",
                "category": "行业特色", 
                "keywords": ["科技公司", "技术驱动", "数据决策", "用户至上"]
            }
        ]
        
        # 批量添加知识
        self.add_documents(base_knowledge)
        logger.info(f"初始化知识库完成，添加了 {len(base_knowledge)} 条记录")
    
    def add_documents(self, documents: List[Dict[str, Any]]):
        """添加文档到向量库"""
        try:
            ids = [doc["id"] for doc in documents]
            contents = [doc["content"] for doc in documents]
            metadatas = [
                {
                    "category": doc.get("category", ""),
                    "keywords": json.dumps(doc.get("keywords", []), ensure_ascii=False)
                }
                for doc in documents
            ]
            
            # 生成嵌入向量
            if self.embedding_model:
                embeddings = self.embedding_model.encode(contents).tolist()
                self.collection.add(
                    ids=ids,
                    documents=contents,
                    metadatas=metadatas,
                    embeddings=embeddings
                )
            else:
                # 使用ChromaDB默认嵌入
                self.collection.add(
                    ids=ids,
                    documents=contents,
                    metadatas=metadatas
                )
            
            logger.info(f"成功添加 {len(documents)} 条文档")
            
        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            raise
    
    def search_relevant_knowledge(
        self, 
        query: str, 
        n_results: int = 3,
        category_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """搜索相关知识"""
        try:
            # 构建查询条件
            where_clause = {}
            if category_filter:
                where_clause["category"] = category_filter
            
            # 执行搜索
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results,
                where=where_clause if where_clause else None
            )
            
            # 格式化结果
            knowledge_items = []
            if results["documents"] and results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    knowledge_items.append({
                        "content": doc,
                        "category": results["metadatas"][0][i].get("category", ""),
                        "keywords": json.loads(results["metadatas"][0][i].get("keywords", "[]")),
                        "distance": results["distances"][0][i] if results["distances"] else 0
                    })
            
            logger.info(f"搜索查询: '{query}', 返回 {len(knowledge_items)} 条结果")
            return knowledge_items
            
        except Exception as e:
            logger.error(f"搜索知识库失败: {e}")
            return []
    
    def get_category_knowledge(self, category: str) -> List[Dict[str, Any]]:
        """获取特定类别的知识"""
        return self.search_relevant_knowledge("", n_results=10, category_filter=category)
    
    def add_user_case(self, user_id: str, company_info: Dict[str, Any], culture_result: Dict[str, Any]):
        """添加用户案例到知识库"""
        try:
            case_id = f"case_{user_id}_{len(self.collection.get()['ids'])}"
            
            # 构建案例内容
            case_content = f"""
            企业案例：
            行业：{company_info.get('industry', '未知')}
            规模：{company_info.get('size', '未知')}
            特点：{company_info.get('features', '未知')}
            
            提炼结果：
            价值观：{culture_result.get('values', '')}
            使命：{culture_result.get('mission', '')}
            愿景：{culture_result.get('vision', '')}
            """
            
            self.add_documents([{
                "id": case_id,
                "content": case_content.strip(),
                "category": "用户案例",
                "keywords": [company_info.get('industry', ''), "案例", "实践"]
            }])
            
            logger.info(f"添加用户案例: {case_id}")
            
        except Exception as e:
            logger.error(f"添加用户案例失败: {e}")

# 全局向量库实例
vector_store = None

def get_vector_store() -> CultureVectorStore:
    """获取向量库实例（单例模式）"""
    global vector_store
    if vector_store is None:
        vector_store = CultureVectorStore()
    return vector_store
