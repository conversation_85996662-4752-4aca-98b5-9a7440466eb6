@echo off
echo 启动企业文化提炼AI助手后端服务...
echo.

cd backend

echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请确保已安装Python 3.9+
    pause
    exit /b 1
)

echo.
echo 安装依赖包...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 错误: 依赖包安装失败
    pause
    exit /b 1
)

echo.
echo 启动FastAPI服务器...
echo 服务将在 http://localhost:8000 启动
echo API文档: http://localhost:8000/docs
echo.

uvicorn main:app --reload --port 8000 --host 0.0.0.0

pause
