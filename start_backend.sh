#!/bin/bash

echo "启动企业文化提炼AI助手后端服务..."
echo

cd backend

echo "检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请确保已安装Python 3.9+"
    exit 1
fi

python3 --version

echo
echo "安装依赖包..."
pip3 install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "错误: 依赖包安装失败"
    exit 1
fi

echo
echo "启动FastAPI服务器..."
echo "服务将在 http://localhost:8000 启动"
echo "API文档: http://localhost:8000/docs"
echo

uvicorn main:app --reload --port 8000 --host 0.0.0.0
