# 企业文化提炼AI助手

基于多Agent协同的企业文化提炼系统，集成本地向量知识库，支持多用户会话管理。

## 功能特性

- **多Agent协同**：信息收集Agent、文化分析师Agent、提炼专家Agent分工合作
- **向量知识库**：集成本地向量数据库，获取相关企业文化资料协助提炼
- **多用户支持**：每个用户独立会话，通过user_id区分
- **流式输出**：实时显示AI思考过程（"打字机效果"）
- **会话管理**：支持查看历史会话和继续未完成流程

## 技术栈

### 后端
- FastAPI - 高性能API框架
- LangGraph - Agent编排框架
- ChromaDB - 向量数据库
- Sentence Transformers - 文本嵌入模型

### 前端
- React 18 + TypeScript
- Vite - 构建工具
- Tailwind CSS - 样式框架
- SSE - 服务器发送事件

## 快速开始

### 1. 环境准备

确保已安装：
- Python 3.9+
- Node.js 16+
- 本地LLM服务（如vLLM、Ollama）

### 2. 启动本地模型服务

```bash
# 使用vLLM启动Qwen模型（示例）
python -m vllm.entrypoints.openai.api_server \
  --model qwen/qwen3-30b-a3b-2507 \
  --host 0.0.0.0 \
  --port 2000
```

### 3. 启动后端

```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --port 8000
```

### 4. 启动前端

```bash
cd frontend
npm install
npm run dev
```

### 5. 访问应用

打开浏览器访问 http://localhost:3000

## 项目结构

```
culture-ai/
├── backend/
│   ├── main.py            # FastAPI入口
│   ├── agents.py          # 核心Agent实现
│   ├── vector_store.py    # 向量知识库
│   └── requirements.txt   # 后端依赖
├── frontend/
│   ├── package.json
│   ├── vite.config.ts
│   └── src/
│       ├── App.tsx        # 主组件
│       ├── index.css
│       └── main.tsx
└── README.md
```

## API文档

启动后端后访问 http://localhost:8000/docs 查看自动生成的API文档。

## 开发说明

详细的开发说明请参考 `mvp.md` 文件。
