// frontend/src/App.tsx
import React, { useState, useEffect, useRef } from 'react';

type ChatMessage = {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
};

type CulturePhase = 'discovery' | 'knowledge_search' | 'analysis' | 'synthesis' | 'complete';

type StreamEvent = {
  content?: string;
  phase?: CulturePhase;
  event?: string;
  error?: string;
};

type SessionInfo = {
  session_id: string;
  created_at: string;
  current_phase: CulturePhase;
  last_message: string;
  message_count: number;
};

function App() {
  const [userInput, setUserInput] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [phase, setPhase] = useState<CulturePhase>('discovery');
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [userSessions, setUserSessions] = useState<SessionInfo[]>([]);
  const [showSessions, setShowSessions] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  // 生成用户ID（实际应用中应该从认证系统获取）
  const userId = 'user_' + Math.random().toString(36).substr(2, 9);

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 获取用户会话列表
  useEffect(() => {
    fetchUserSessions();
  }, []);

  const fetchUserSessions = async () => {
    try {
      const response = await fetch(`http://localhost:8000/sessions/${userId}`);
      if (response.ok) {
        const data = await response.json();
        setUserSessions(data.sessions);
      }
    } catch (error) {
      console.error('获取会话失败:', error);
    }
  };

  const startNewSession = () => {
    setMessages([]);
    setPhase('discovery');
    setSessionId(null);
    setUserInput('');
    setShowSessions(false);
  };

  const loadSession = async (session: SessionInfo) => {
    try {
      const response = await fetch(`http://localhost:8000/sessions/${userId}/${session.session_id}`);
      if (response.ok) {
        const data = await response.json();
        setSessionId(session.session_id);
        setPhase(data.current_phase);
        setMessages(data.messages);
        setShowSessions(false);
      }
    } catch (error) {
      console.error('加载会话失败:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userInput.trim() || isLoading) return;

    // 添加用户消息
    const newMessage: ChatMessage = { 
      role: 'user', 
      content: userInput,
      timestamp: new Date().toISOString()
    };
    setMessages(prev => [...prev, newMessage]);
    setUserInput('');
    setIsLoading(true);

    try {
      let url: string;
      let requestData: any;

      if (sessionId) {
        // 继续已有会话
        url = 'http://localhost:8000/culture/continue';
        requestData = {
          user_id: userId,
          session_id: sessionId,
          input: userInput
        };
      } else {
        // 新会话
        url = 'http://localhost:8000/culture/start';
        requestData = {
          user_id: userId,
          input: userInput
        };
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 建立SSE连接
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let currentAssistantMessage = '';

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data: StreamEvent = JSON.parse(line.slice(6));

                if (data.error) {
                  setMessages(prev => [...prev, { 
                    role: 'assistant', 
                    content: `系统错误: ${data.error}`,
                    timestamp: new Date().toISOString()
                  }]);
                  setIsLoading(false);
                  return;
                }

                if (data.event === 'complete') {
                  setIsLoading(false);
                  fetchUserSessions();
                  return;
                }

                if (data.content) {
                  currentAssistantMessage += data.content;
                  setMessages(prev => {
                    const lastMsg = prev[prev.length - 1];
                    if (lastMsg?.role === 'assistant') {
                      return [
                        ...prev.slice(0, -1),
                        { 
                          role: 'assistant', 
                          content: currentAssistantMessage,
                          timestamp: new Date().toISOString()
                        }
                      ];
                    }
                    return [...prev, { 
                      role: 'assistant', 
                      content: currentAssistantMessage,
                      timestamp: new Date().toISOString()
                    }];
                  });
                }

                if (data.phase) {
                  setPhase(data.phase);
                }
              } catch (error) {
                console.error('解析事件失败:', error);
              }
            }
          }
        }
      }

    } catch (error) {
      console.error('请求失败:', error);
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: '抱歉，发生网络错误，请重试',
        timestamp: new Date().toISOString()
      }]);
      setIsLoading(false);
    }
  };

  const renderPhaseBadge = () => {
    const phaseNames: Record<CulturePhase, string> = {
      discovery: '信息收集',
      knowledge_search: '知识搜索',
      analysis: '文化分析',
      synthesis: '框架提炼',
      complete: '完成'
    };

    const phaseColors: Record<CulturePhase, string> = {
      discovery: 'bg-blue-500',
      knowledge_search: 'bg-purple-500',
      analysis: 'bg-orange-500',
      synthesis: 'bg-green-500',
      complete: 'bg-gray-500'
    };

    return (
      <span className={`px-3 py-1 rounded-full text-white text-sm font-medium ${phaseColors[phase]} phase-indicator`}>
        {phaseNames[phase]}
      </span>
    );
  };

  const formatTime = (timestamp?: string) => {
    if (!timestamp) return '';
    return new Date(timestamp).toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-6xl mx-auto p-4">
        {/* 头部 */}
        <header className="mb-6 bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">企业文化提炼助手</h1>
              <p className="text-gray-600">基于多Agent协同的智能文化提炼系统</p>
            </div>
            <div className="flex items-center space-x-4">
              {renderPhaseBadge()}
              <button 
                onClick={() => setShowSessions(!showSessions)}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                {showSessions ? '隐藏会话' : '历史会话'}
              </button>
              <button 
                onClick={startNewSession}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                新建会话
              </button>
            </div>
          </div>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 会话列表 */}
          {showSessions && (
            <div className="lg:col-span-1 bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-4 border-b bg-gray-50">
                <h2 className="font-semibold text-gray-800">历史会话</h2>
                <p className="text-sm text-gray-600">共 {userSessions.length} 个会话</p>
              </div>
              <div className="overflow-y-auto" style={{ maxHeight: '60vh' }}>
                {userSessions.length === 0 ? (
                  <div className="p-4 text-gray-500 text-sm text-center">
                    暂无历史会话
                  </div>
                ) : (
                  userSessions.map((session) => (
                    <div 
                      key={session.session_id}
                      className={`p-4 border-b hover:bg-gray-50 cursor-pointer transition-colors ${
                        sessionId === session.session_id ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                      onClick={() => loadSession(session)}
                    >
                      <div className="font-medium text-gray-800 truncate mb-1">
                        {session.last_message}
                      </div>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{session.created_at}</span>
                        <span className="bg-gray-200 px-2 py-1 rounded">
                          {session.message_count} 条消息
                        </span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}

          {/* 聊天主区域 */}
          <div className={showSessions ? "lg:col-span-3" : "lg:col-span-4"}>
            <div className="bg-white rounded-lg shadow-sm h-[70vh] flex flex-col">
              {/* 消息区域 */}
              <div className="flex-1 overflow-y-auto p-6 space-y-4">
                {messages.length === 0 && (
                  <div className="text-center text-gray-500 mt-20">
                    <div className="text-6xl mb-4">🏢</div>
                    <h3 className="text-xl font-semibold mb-2">欢迎使用企业文化提炼助手</h3>
                    <p className="text-gray-600">请描述您的企业基本情况，我将帮助您提炼独特的企业文化</p>
                  </div>
                )}
                
                {messages.map((msg, index) => (
                  <div 
                    key={index} 
                    className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'} message-fade-in`}
                  >
                    <div className={`max-w-[80%] rounded-lg p-4 ${
                      msg.role === 'user' 
                        ? 'bg-indigo-600 text-white' 
                        : 'bg-gray-100 text-gray-800 border'
                    }`}>
                      <div className="whitespace-pre-wrap">{msg.content}</div>
                      {msg.timestamp && (
                        <div className={`text-xs mt-2 ${
                          msg.role === 'user' ? 'text-indigo-200' : 'text-gray-500'
                        }`}>
                          {formatTime(msg.timestamp)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                
                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 rounded-lg p-4 border">
                      <div className="flex items-center space-x-2">
                        <div className="typing-indicator"></div>
                        <div className="typing-indicator"></div>
                        <div className="typing-indicator"></div>
                        <span className="text-gray-600 ml-2">AI正在思考...</span>
                      </div>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>

              {/* 输入区域 */}
              <div className="border-t p-4 bg-gray-50">
                <form onSubmit={handleSubmit} className="flex gap-3">
                  <input
                    type="text"
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    placeholder="请输入您的企业信息..."
                    className="flex-1 p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    disabled={isLoading}
                  />
                  <button
                    type="submit"
                    disabled={isLoading || !userInput.trim()}
                    className={`px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors ${
                      isLoading || !userInput.trim() ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    {isLoading ? '发送中...' : '发送'}
                  </button>
                </form>
                <p className="mt-2 text-xs text-gray-500 text-center">
                  正在使用Qwen3-30B模型 • 集成向量知识库协助提炼
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
