#!/usr/bin/env python3
"""
企业文化提炼AI助手系统测试脚本
"""

import asyncio
import httpx
import json
import time
from typing import Dict, Any

class SystemTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_user_id = "test_user_123"
        
    async def test_health_check(self) -> bool:
        """测试健康检查端点"""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查通过: {data['status']}")
                print(f"   知识库状态: {data.get('knowledge_base', 'unknown')}")
                print(f"   知识条目数: {data.get('knowledge_count', 0)}")
                return True
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    async def test_knowledge_search(self) -> bool:
        """测试知识库搜索"""
        try:
            response = await self.client.get(
                f"{self.base_url}/knowledge/search",
                params={"query": "企业价值观", "limit": 3}
            )
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 知识库搜索成功: 找到 {data['count']} 条结果")
                for i, result in enumerate(data['results'][:2]):
                    print(f"   {i+1}. {result['category']}: {result['content'][:50]}...")
                return True
            else:
                print(f"❌ 知识库搜索失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 知识库搜索异常: {e}")
            return False
    
    async def test_culture_workflow(self) -> bool:
        """测试完整的文化提炼工作流"""
        try:
            print("🚀 开始测试文化提炼工作流...")
            
            # 启动新会话
            start_data = {
                "user_id": self.test_user_id,
                "input": "我们是一家专注于AI工具开发的科技创业公司，团队有15人，主要服务B端客户"
            }
            
            response = await self.client.post(
                f"{self.base_url}/culture/start",
                json=start_data
            )
            
            if response.status_code != 200:
                print(f"❌ 启动会话失败: HTTP {response.status_code}")
                return False
            
            print("✅ 会话启动成功，开始接收流式响应...")
            
            # 解析流式响应
            content = response.content.decode('utf-8')
            lines = content.split('\n')
            
            assistant_responses = []
            current_phase = "discovery"
            
            for line in lines:
                if line.startswith('data: '):
                    try:
                        data = json.loads(line[6:])
                        if 'content' in data:
                            assistant_responses.append(data['content'])
                        if 'phase' in data:
                            current_phase = data['phase']
                        if data.get('event') == 'complete':
                            break
                    except json.JSONDecodeError:
                        continue
            
            if assistant_responses:
                print(f"✅ 收到 {len(assistant_responses)} 条AI响应")
                print(f"   最终阶段: {current_phase}")
                print(f"   首条响应: {assistant_responses[0][:100]}...")
                return True
            else:
                print("❌ 未收到有效的AI响应")
                return False
                
        except Exception as e:
            print(f"❌ 工作流测试异常: {e}")
            return False
    
    async def test_session_management(self) -> bool:
        """测试会话管理功能"""
        try:
            # 获取用户会话列表
            response = await self.client.get(f"{self.base_url}/sessions/{self.test_user_id}")
            if response.status_code == 200:
                data = response.json()
                session_count = len(data['sessions'])
                print(f"✅ 会话管理测试通过: 找到 {session_count} 个会话")
                
                if session_count > 0:
                    # 测试获取会话详情
                    session_id = data['sessions'][0]['session_id']
                    detail_response = await self.client.get(
                        f"{self.base_url}/sessions/{self.test_user_id}/{session_id}"
                    )
                    if detail_response.status_code == 200:
                        detail_data = detail_response.json()
                        print(f"   会话详情: {len(detail_data['messages'])} 条消息")
                        print(f"   当前阶段: {detail_data['current_phase']}")
                
                return True
            else:
                print(f"❌ 会话管理测试失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 会话管理测试异常: {e}")
            return False
    
    async def test_debug_endpoints(self) -> bool:
        """测试调试端点"""
        try:
            # 测试工作流信息
            response = await self.client.get(f"{self.base_url}/debug/workflow")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 工作流信息: {len(data['phases'])} 个阶段")
                print(f"   Agent数量: {len(data['agents'])}")
            
            # 测试统计信息
            stats_response = await self.client.get(f"{self.base_url}/debug/stats")
            if stats_response.status_code == 200:
                stats_data = stats_response.json()
                print(f"✅ 系统统计: {stats_data['total_users']} 用户, {stats_data['total_sessions']} 会话")
                print(f"   知识库大小: {stats_data['knowledge_base_size']} 条")
            
            return True
        except Exception as e:
            print(f"❌ 调试端点测试异常: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("企业文化提炼AI助手 - 系统测试")
        print("=" * 60)
        
        tests = [
            ("健康检查", self.test_health_check),
            ("知识库搜索", self.test_knowledge_search),
            ("会话管理", self.test_session_management),
            ("调试端点", self.test_debug_endpoints),
            ("文化提炼工作流", self.test_culture_workflow),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 测试: {test_name}")
            print("-" * 40)
            
            start_time = time.time()
            try:
                result = await test_func()
                elapsed = time.time() - start_time
                
                if result:
                    print(f"✅ {test_name} 通过 ({elapsed:.2f}s)")
                    passed += 1
                else:
                    print(f"❌ {test_name} 失败 ({elapsed:.2f}s)")
            except Exception as e:
                elapsed = time.time() - start_time
                print(f"💥 {test_name} 异常: {e} ({elapsed:.2f}s)")
        
        print("\n" + "=" * 60)
        print(f"测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！系统运行正常")
        else:
            print("⚠️  部分测试失败，请检查系统配置")
        
        await self.client.aclose()

async def main():
    """主函数"""
    print("请确保后端服务已启动在 http://localhost:8000")
    print("如果需要，请先运行: python -m uvicorn main:app --reload --port 8000")
    
    input("\n按回车键开始测试...")
    
    tester = SystemTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
