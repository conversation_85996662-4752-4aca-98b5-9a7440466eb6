@echo off
echo 启动企业文化提炼AI助手前端界面...
echo.

cd frontend

echo 检查Node.js环境...
node --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请确保已安装Node.js 16+
    pause
    exit /b 1
)

npm --version
if %errorlevel% neq 0 (
    echo 错误: 未找到npm
    pause
    exit /b 1
)

echo.
echo 安装依赖包...
npm install
if %errorlevel% neq 0 (
    echo 错误: 依赖包安装失败
    pause
    exit /b 1
)

echo.
echo 启动开发服务器...
echo 前端将在 http://localhost:3000 启动
echo.

npm run dev

pause
